'use client'
import React, { useEffect, useState } from 'react'
import { useLazyQuery } from '@apollo/client'
import { GetRiskFactors } from '@/app/lib/graphQL/query'
import RiskAnalysis from '../logbook/forms/risk-analysis'
import BarCrossingRiskAnalysis from '../logbook/forms/bar-crossing-risk-analysis'
import {
    CollapsibleDataTable,
    createColumns,
} from '@/components/collapsible-data-table'
import { format } from 'date-fns'
import { ListHeader } from '@/components/ui'

// Define the data type for risk evaluations
interface RiskEvaluation {
    id: number
    type: string
    vessel?: {
        id: number
        title: string
    }
    created: string
    towingChecklistID?: number
    dangerousGoodsID?: number
    barCrossingChecklistID?: number
    towingChecklist?: {
        member?: {
            firstName?: string
            surname?: string
        }
    }
    dangerousGoods?: {
        member?: {
            firstName?: string
            surname?: string
        }
    }
    barCrossingChecklist?: {
        member?: {
            firstName?: string
            surname?: string
        }
    }
}

// Helper function to get the display name for risk evaluation type
const getRiskEvaluationType = (checklist: RiskEvaluation): string => {
    const typeMap: Record<string, string> = {
        TowingChecklist: 'Towing Checklist',
        DangerousGoods: 'Dangerous Goods',
        BarCrossingChecklist: 'Bar Crossing Checklist',
    }

    let typeName = typeMap[checklist.type] || checklist.type

    // Add ID if available
    if (
        checklist.type === 'TowingChecklist' &&
        checklist.towingChecklistID &&
        checklist.towingChecklistID > 0
    ) {
        typeName += `: #${checklist.towingChecklistID}`
    } else if (
        checklist.type === 'DangerousGoods' &&
        checklist.dangerousGoodsID &&
        checklist.dangerousGoodsID > 0
    ) {
        typeName += `: #${checklist.dangerousGoodsID}`
    } else if (
        checklist.type === 'BarCrossingChecklist' &&
        checklist.barCrossingChecklistID &&
        checklist.barCrossingChecklistID > 0
    ) {
        typeName += `: #${checklist.barCrossingChecklistID}`
    }

    return typeName
}

// Helper function to get member name
const getMemberName = (checklist: RiskEvaluation): string => {
    let member = null

    if (checklist.type === 'TowingChecklist') {
        member = checklist.towingChecklist?.member
    } else if (checklist.type === 'DangerousGoods') {
        member = checklist.dangerousGoods?.member
    } else if (checklist.type === 'BarCrossingChecklist') {
        member = checklist.barCrossingChecklist?.member
    }

    if (!member) return ''

    const firstName = member.firstName || ''
    const surname = member.surname || ''
    return `${firstName} ${surname}`.trim()
}

// Helper function to format date
const formatDate = (dateString: string): string => {
    if (!dateString) return ''
    try {
        return format(new Date(dateString), 'dd/MM/yyyy')
    } catch {
        return dateString
    }
}

export default function RiskEvaluations() {
    const [riskFactors, setRiskFactors] = useState<RiskEvaluation[]>([])
    const [allChecked, setAllChecked] = useState<any>(false)

    // Define columns for the collapsible table
    const columns = createColumns<RiskEvaluation>([
        {
            accessorKey: 'type',
            header: 'Risk Evaluations',
            cell: ({ row }) => (
                <div className="w-full space-y-2 landscape:space-y-0">
                    <div className="flex flex-wrap justify-between items-center">
                        <div className="small:text-nowrap">
                            {getRiskEvaluationType(row.original)}
                        </div>
                        {getMemberName(row.original) && (
                            <div className="tablet-md:hidden text-card-foreground/80">
                                Memeber: {getMemberName(row.original)}
                            </div>
                        )}
                    </div>

                    <div className="text-sm landscape:hidden flex justify-between text-card-foreground/80">
                        <div>
                            Date:{' '}
                            <span>{formatDate(row.original.created)}</span>
                        </div>
                        <div className=" tablet-md:hidden">
                            {row.original.vessel?.title || ''}
                        </div>
                    </div>
                </div>
            ),
            cellAlignment: 'left',
        },
        {
            accessorKey: 'vessel.title',
            header: 'Vessel',
            breakpoint: 'tablet-md',
            cell: ({ row }) => <div>{row.original.vessel?.title || ''}</div>,
            cellAlignment: 'center',
        },
        {
            accessorKey: 'created',
            header: 'Date',
            breakpoint: 'landscape',
            cell: ({ row }) => <div>{formatDate(row.original.created)}</div>,
            cellAlignment: 'center',
        },
        {
            accessorKey: 'member',
            header: 'Member',
            breakpoint: 'tablet-md',
            cell: ({ row }) => <div>{getMemberName(row.original)}</div>,
            cellAlignment: 'right',
        },
    ])

    // Function to render expanded content
    const renderExpandedContent = (rowData: RiskEvaluation) => {
        return (
            <div className="p-4">
                {rowData.type === 'TowingChecklist' &&
                    rowData.towingChecklistID && (
                        <RiskAnalysis
                            selectedEvent={false}
                            onSidebarClose={false}
                            logBookConfig={false}
                            currentTrip={false}
                            crewMembers={false}
                            towingChecklistID={rowData.towingChecklistID}
                            setTowingChecklistID={() => {}}
                            setAllChecked={setAllChecked}
                            noSheet={true}
                        />
                    )}
                {rowData.type === 'BarCrossingChecklist' &&
                    rowData.barCrossingChecklistID && (
                        <BarCrossingRiskAnalysis
                            selectedEvent={false}
                            onSidebarClose={false}
                            logBookConfig={false}
                            currentTrip={false}
                            crewMembers={false}
                            barCrossingChecklistID={
                                rowData.barCrossingChecklistID
                            }
                            setBarCrossingChecklistID={() => {}}
                            setAllChecked={setAllChecked}
                            noSheet={true}
                        />
                    )}
                {/* Dangerous Goods analysis can be added here when available */}
            </div>
        )
    }

    // Function to determine if a row can be expanded
    const canExpand = (rowData: RiskEvaluation): boolean => {
        return (
            (rowData.type === 'TowingChecklist' &&
                !!rowData.towingChecklistID) ||
            (rowData.type === 'BarCrossingChecklist' &&
                !!rowData.barCrossingChecklistID)
            // Add DangerousGoods when available
        )
    }

    useEffect(() => {
        getRiskFactors({
            variables: {
                filter: { type: { ne: 'RiskFactor' } },
            },
        })
    }, [])

    const [getRiskFactors] = useLazyQuery(GetRiskFactors, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            setRiskFactors(data.readRiskFactors.nodes)
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    return (
        <>
            <ListHeader title="Risk Evaluations" />

            {riskFactors && riskFactors.length > 0 && (
                <CollapsibleDataTable
                    columns={columns}
                    data={riskFactors}
                    showToolbar={false}
                    collapsible={true}
                    renderExpandedContent={renderExpandedContent}
                    canExpand={canExpand}
                    pageSize={20}
                    showPageSizeSelector={false}
                />
            )}
        </>
    )
}
