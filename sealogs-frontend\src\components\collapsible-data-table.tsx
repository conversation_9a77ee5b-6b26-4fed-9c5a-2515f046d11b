'use client'
import {
    Table,
    TableBody,
    TableHeader,
    TableRow,
    TableHead,
    TableCell,
} from '@/components/ui/table'
import { DataTableToolbar } from './data-table-toolbar'
import { DataTablePagination } from './data-table-pagination'
import { useBreakpoints } from './hooks/useBreakpoints'
import * as React from 'react'
import {
    type ColumnDef,
    type ColumnFiltersState,
    type SortingState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
    type Row,
} from '@tanstack/react-table'
import { Card } from './ui/card'
import { cn } from '@/app/lib/utils'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'

type BreakpointKey =
    | 'tiny'
    | 'small'
    | 'standard'
    | 'phablet'
    | 'tablet-sm'
    | 'tablet-md'
    | 'tablet-lg'
    | 'landscape'
    | 'laptop'
    | 'desktop'

// Extended ColumnDef type with cellAlignment and breakpoint properties
export type ExtendedColumnDef<TData, TValue = unknown> = ColumnDef<
    TData,
    TValue
> & {
    /** Controls the text alignment for this column's header and cells. Defaults to 'center'. Note: 'title' column is always left-aligned. */
    cellAlignment?: 'left' | 'center' | 'right'
    /** Minimum breakpoint at which this column should be visible. Column will be hidden on smaller screens. */
    breakpoint?: BreakpointKey
    /** Maximum breakpoint at which this column should be visible. Column will be hidden on larger screens. */
    showOnlyBelow?: BreakpointKey
}

/**
 * Helper function to create columns with proper typing inference
 * Eliminates the need to explicitly type column arrays
 */
export function createColumns<TData = any>(
    columns: ExtendedColumnDef<TData, any>[],
): ExtendedColumnDef<TData, any>[] {
    return columns
}

// Row status types for highlighting
export type RowStatus = 'overdue' | 'upcoming' | 'normal'

// Function type for evaluating row status
export type RowStatusEvaluator<TData> = (rowData: TData) => RowStatus

// Function type for rendering expanded content
export type ExpandedContentRenderer<TData> = (
    rowData: TData,
    row: Row<TData>,
) => React.ReactNode

interface CollapsibleDataTableProps<TData, TValue> {
    columns: ExtendedColumnDef<TData, TValue>[]
    data: TData[]
    showToolbar?: boolean
    className?: string
    pageSize?: number
    pageSizeOptions?: number[]
    showPageSizeSelector?: boolean
    onChange?: any
    /** Optional function to evaluate row status for highlighting. Returns 'overdue', 'upcoming', or 'normal' */
    rowStatus?: RowStatusEvaluator<TData>
    /** Enable collapsible rows */
    collapsible?: boolean
    /** Function to render expanded content for each row */
    renderExpandedContent?: ExpandedContentRenderer<TData>
    /** Function to determine if a row can be expanded */
    canExpand?: (rowData: TData) => boolean
    /** Default expanded state for all rows */
    defaultExpanded?: boolean
}

// Helper function to get alignment classes based on cellAlignment prop
const getAlignmentClasses = (alignment: 'left' | 'center' | 'right') => {
    switch (alignment) {
        case 'left':
            return 'items-left justify-start justify-items-start text-left'
        case 'right':
            return 'items-right justify-end justify-items-end text-right'
        case 'center':
        default:
            return 'items-center justify-center justify-items-center text-center'
    }
}

// Helper function to get row status background classes
const getRowStatusClasses = (status: RowStatus) => {
    switch (status) {
        case 'overdue':
            return 'bg-destructive-200/70 [&>td:first-child]:rounded-l-lg [&>td:last-child]:rounded-r-lg [&>td>span>span]:bg-destructive-200'
        case 'upcoming':
            return 'bg-warning-100/70 [&>td:first-child]:rounded-l-lg [&>td:last-child]:rounded-r-lg [&>td>span>span]:bg-warning-100'
        case 'normal':
        default:
            return ''
    }
}

export function CollapsibleDataTable<TData, TValue>({
    columns,
    data,
    showToolbar = true,
    className,
    pageSize = 10,
    pageSizeOptions = [10, 20, 30, 40, 50],
    showPageSizeSelector = true,
    onChange,
    rowStatus,
    collapsible = false,
    renderExpandedContent,
    canExpand,
    defaultExpanded = false,
}: CollapsibleDataTableProps<TData, TValue>) {
    const [sorting, setSorting] = React.useState<SortingState>([])
    const [columnFilters, setColumnFilters] =
        React.useState<ColumnFiltersState>([])
    const [pagination, setPagination] = React.useState({
        pageIndex: 0,
        pageSize: pageSize,
    })

    // State for tracking expanded rows
    const [expandedRows, setExpandedRows] = React.useState<Set<string>>(
        defaultExpanded
            ? new Set(data.map((_, index) => index.toString()))
            : new Set(),
    )

    // Get current breakpoint states
    const breakpoints = useBreakpoints()

    // Add expand column if collapsible is enabled
    const columnsWithExpand = React.useMemo(() => {
        if (!collapsible) return columns

        const expandColumn: ExtendedColumnDef<TData, TValue> = {
            id: 'expand',
            header: '',
            cell: ({ row }) => {
                const rowData = row.original
                const rowId = row.id
                const isExpanded = expandedRows.has(rowId)
                const canExpandRow = canExpand ? canExpand(rowData) : true

                if (!canExpandRow) {
                    return <div className="w-8" />
                }

                return (
                    <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 hover:bg-accent"
                        onClick={(e) => {
                            e.stopPropagation()
                            setExpandedRows((prev) => {
                                const newSet = new Set(prev)
                                if (isExpanded) {
                                    newSet.delete(rowId)
                                } else {
                                    newSet.add(rowId)
                                }
                                return newSet
                            })
                        }}>
                        {isExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                        ) : (
                            <ChevronRight className="h-4 w-4" />
                        )}
                    </Button>
                )
            },
            size: 40,
            cellAlignment: 'center',
        }

        return [expandColumn, ...columns]
    }, [columns, collapsible, expandedRows, canExpand])

    // Filter columns based on breakpoint visibility
    const visibleColumns = React.useMemo(() => {
        return columnsWithExpand.filter((column) => {
            const extendedColumn = column as ExtendedColumnDef<TData, TValue>

            // Handle showOnlyBelow breakpoint (show only on smaller screens)
            if (extendedColumn.showOnlyBelow) {
                return !breakpoints[extendedColumn.showOnlyBelow]
            }

            // Handle regular breakpoint (show only on larger screens)
            if (extendedColumn.breakpoint) {
                return breakpoints[extendedColumn.breakpoint]
            }

            // If no breakpoint is specified, column is always visible
            return true
        })
    }, [columnsWithExpand, breakpoints])

    // Update pagination when pageSize prop changes
    React.useEffect(() => {
        setPagination((prev) => ({
            ...prev,
            pageSize: pageSize,
        }))
    }, [pageSize])

    const table = useReactTable({
        data,
        columns: visibleColumns,
        onSortingChange: setSorting,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onColumnFiltersChange: setColumnFilters,
        getFilteredRowModel: getFilteredRowModel(),
        onPaginationChange: setPagination,
        state: {
            sorting,
            columnFilters,
            pagination,
        },
    })

    // Function to toggle all rows
    const toggleAllRows = () => {
        const allRowIds = table.getRowModel().rows.map((row) => row.id)
        const expandableRowIds = allRowIds.filter((rowId) => {
            const row = table.getRowModel().rows.find((r) => r.id === rowId)
            return row && (canExpand ? canExpand(row.original) : true)
        })

        const allExpanded = expandableRowIds.every((id) => expandedRows.has(id))

        if (allExpanded) {
            setExpandedRows(new Set())
        } else {
            setExpandedRows(new Set(expandableRowIds))
        }
    }

    return (
        <div className="space-y-4 pb-8">
            {showToolbar && (
                <Card className="p-2 md:p-auto">
                    <div className="flex items-center justify-between">
                        <DataTableToolbar table={table} onChange={onChange} />
                        {collapsible && (
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={toggleAllRows}
                                className="ml-2">
                                {expandedRows.size > 0
                                    ? 'Collapse All'
                                    : 'Expand All'}
                            </Button>
                        )}
                    </div>
                </Card>
            )}

            <Table
                className={
                    className ||
                    'p-0 phablet:p-8 lg:p-6 xl:p-8 shadow-none border-0 phablet:border border-border bg-card rounded-lg'
                }>
                {table
                    .getHeaderGroups()
                    .some((headerGroup) =>
                        headerGroup.headers.some(
                            (header) =>
                                header.column.columnDef.header &&
                                header.column.columnDef.header !== '',
                        ),
                    ) && (
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => {
                                    const columnDef = header.column
                                        .columnDef as ExtendedColumnDef<
                                        TData,
                                        TValue
                                    >
                                    const alignment =
                                        header.column.id === 'title'
                                            ? 'left'
                                            : columnDef.cellAlignment ||
                                              'center'

                                    return (
                                        <TableHead
                                            key={header.id}
                                            className={
                                                header.column.id === 'title'
                                                    ? 'items-left justify-items-start text-left'
                                                    : getAlignmentClasses(
                                                          alignment,
                                                      )
                                            }>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                      header.column.columnDef
                                                          .header,
                                                      header.getContext(),
                                                  )}
                                        </TableHead>
                                    )
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                )}
                <TableBody>
                    {table.getRowModel().rows.length ? (
                        table.getRowModel().rows.map((row) => {
                            // Evaluate row status if rowStatus function is provided
                            const status = rowStatus
                                ? rowStatus(row.original)
                                : 'normal'
                            const statusClasses = getRowStatusClasses(status)
                            const isExpanded = expandedRows.has(row.id)

                            return (
                                <React.Fragment key={String(row.id)}>
                                    <TableRow
                                        data-state={
                                            row.getIsSelected()
                                                ? 'selected'
                                                : undefined
                                        }
                                        className={cn('', statusClasses)}>
                                        {row.getVisibleCells().map((cell) => {
                                            const columnDef = cell.column
                                                .columnDef as ExtendedColumnDef<
                                                TData,
                                                TValue
                                            >
                                            const alignment =
                                                cell.column.id === 'title'
                                                    ? 'left'
                                                    : columnDef.cellAlignment ||
                                                      'center'

                                            return (
                                                <TableCell
                                                    key={cell.id}
                                                    className={
                                                        cell.column.id ===
                                                        'title'
                                                            ? `${visibleColumns.length > 1 ? 'w-auto' : 'w-full'} items-left justify-items-start text-left`
                                                            : getAlignmentClasses(
                                                                  alignment,
                                                              )
                                                    }>
                                                    <div
                                                        className={cn(
                                                            'flex flex-1',
                                                            getAlignmentClasses(
                                                                alignment,
                                                            ),
                                                        )}>
                                                        {flexRender(
                                                            cell.column
                                                                .columnDef.cell,
                                                            cell.getContext(),
                                                        )}
                                                    </div>
                                                </TableCell>
                                            )
                                        })}
                                    </TableRow>

                                    {/* Expanded content row */}
                                    {collapsible &&
                                        isExpanded &&
                                        renderExpandedContent && (
                                            <TableRow>
                                                <TableCell
                                                    noHoverEffect
                                                    colSpan={
                                                        visibleColumns.length
                                                    }
                                                    className="p-0 border-b-0">
                                                    <div className="p-1 xs:px-4 xs:py-3 bg-popover border-l-4 border-primary/20">
                                                        {renderExpandedContent(
                                                            row.original,
                                                            row,
                                                        )}
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        )}
                                </React.Fragment>
                            )
                        })
                    ) : (
                        <TableRow>
                            <TableCell
                                colSpan={visibleColumns.length}
                                className="h-24 text-center">
                                No results.
                            </TableCell>
                        </TableRow>
                    )}
                </TableBody>
            </Table>

            {(table.getCanPreviousPage() || table.getCanNextPage()) && (
                <div className="flex items-center justify-center phablet:justify-end space-x-2 py-4">
                    <DataTablePagination
                        table={table}
                        pageSizeOptions={pageSizeOptions}
                        showPageSizeSelector={showPageSizeSelector}
                    />
                </div>
            )}
        </div>
    )
}

// Export for backward compatibility
export const DataTable = CollapsibleDataTable
export const FilteredTable = CollapsibleDataTable
